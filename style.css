* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #a8e6a3 0%, #88d982 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    position: relative;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h2 {
    color: #2c5530;
    font-weight: bold;
    font-size: 24px;
    line-height: 1.2;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav a {
    text-decoration: none;
    color: #2c5530;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 1px;
    transition: color 0.3s ease;
}

.nav a.active,
.nav a:hover {
    color: #1a3d1f;
}

.header-icons {
    display: flex;
    gap: 20px;
}

.header-icons i {
    color: #2c5530;
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.header-icons i:hover {
    color: #1a3d1f;
}

/* Main Content */
.main-content {
    padding: 60px 0;
    min-height: calc(100vh - 100px);
}

.content-wrapper {
    display: flex;
    align-items: center;
    gap: 60px;
    min-height: 500px;
}

/* Left Content */
.left-content {
    flex: 1;
    position: relative;
}

.side-text {
    position: absolute;
    left: -80px;
    top: 50%;
    transform: translateY(-50%) rotate(-90deg);
    transform-origin: center;
}

.side-text span {
    color: #2c5530;
    font-size: 12px;
    letter-spacing: 2px;
    font-weight: 500;
}

.main-text {
    display: flex;
    align-items: flex-start;
    gap: 30px;
}

.number {
    font-size: 120px;
    font-weight: bold;
    color: rgba(44, 85, 48, 0.3);
    line-height: 1;
}

.text-content {
    flex: 1;
    padding-top: 20px;
}

.subtitle {
    color: #2c5530;
    font-size: 12px;
    letter-spacing: 2px;
    font-weight: 500;
    margin-bottom: 20px;
    display: block;
}

.text-content h1 {
    color: #1a3d1f;
    font-size: 48px;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 40px;
}

.cta-button {
    background: transparent;
    border: 2px solid #2c5530;
    color: #2c5530;
    padding: 15px 30px;
    font-size: 12px;
    letter-spacing: 2px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cta-button:hover {
    background: #2c5530;
    color: white;
}

.social-icons {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.social-icons a {
    color: #2c5530;
    font-size: 18px;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: #1a3d1f;
}

/* Right Content */
.right-content {
    flex: 1;
    position: relative;
}

.image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.image-tag {
    position: absolute;
    top: 20px;
    left: 20px;
    background: #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 10;
}

.image-container img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
}

.navigation-arrows {
    position: absolute;
    bottom: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.nav-arrow {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-arrow:hover {
    background: white;
    transform: scale(1.1);
}

.nav-arrow i {
    color: #333;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-wrapper {
        flex-direction: column;
        gap: 40px;
    }
    
    .side-text {
        display: none;
    }
    
    .main-text {
        flex-direction: column;
        gap: 20px;
    }
    
    .number {
        font-size: 80px;
    }
    
    .text-content h1 {
        font-size: 32px;
    }
    
    .nav ul {
        gap: 20px;
    }
    
    .header .container {
        flex-wrap: wrap;
        gap: 20px;
    }
}
